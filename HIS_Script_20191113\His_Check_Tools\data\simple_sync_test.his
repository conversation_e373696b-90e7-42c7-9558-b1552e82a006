# 简化同步消息处理测试脚本
# 专门用于HisUI工具验证核心功能

# ========== 全局变量 ==========
queueSize = 0
processingState = 0
currentMsgId = ""
waitingForResponse = 0
cloudConnected = 0
testCounter = 0

# 简单队列（只存储一条消息用于测试）
queueMessage = ""

# ========== 辅助函数 ==========
FUNCTION extractId(fullMessage)
    msgLen = fullMessage.length()
    IF (msgLen > 8)
        # 提取ID（倒数第8-5字节）
        msgId = fullMessage.subString(msgLen - 8, msgLen - 4)
        RETURN(msgId)
    END
    RETURN("")
END

FUNCTION extractCmd(fullMessage)
    msgLen = fullMessage.length()
    IF (msgLen > 8)
        # 提取原始命令（除去最后8字节）
        originalCmd = fullMessage.subString(0, msgLen - 8)
        RETURN(originalCmd)
    END
    RETURN("")
END

# ========== 连接管理 ==========
CONN SOCK netp
    cloudConnected = 1
    OUTPUT = "[INFO] Cloud connected"
END

DISCONN SOCK netp
    cloudConnected = 0
    processingState = 0
    waitingForResponse = 0
    OUTPUT = "[INFO] Cloud disconnected"
END

# ========== 消息接收测试 ==========
RECV SOCK netp
    IF (cloudConnected == 1)
        testCounter = testCounter + 1
        
        # 简单队列：只存储一条消息
        IF (queueSize == 0)
            queueMessage = INPUT
            queueSize = 1
            OUTPUT = "[TEST] Message #" + testCounter.prtString() + " queued successfully"
        ELSE
            OUTPUT = "[TEST] Message #" + testCounter.prtString() + " dropped - queue full"
        END
    ELSE
        OUTPUT = "[ERROR] Not connected to cloud"
    END
    RETURN(FALSE)
END

# ========== 消息处理测试 ==========
TIMER MessageProcessor 1000
    IF (processingState == 0)
        IF (queueSize > 0)
            # 处理队列中的消息
            fullMessage = queueMessage
            queueMessage = ""
            queueSize = 0
            
            # 解析消息
            originalCmd = extractCmd(fullMessage)
            currentMsgId = extractId(fullMessage)
            
            IF (originalCmd != "")
                # 发送给设备
                OUTPUT = "[PROCESS] Sending command to device: " + originalCmd.prtString()
                processingState = 1
                waitingForResponse = 1
            ELSE
                OUTPUT = "[ERROR] Invalid message format"
            END
        END
    END
END

# ========== 设备响应测试 ==========
RECV UART uart0
    IF (waitingForResponse == 1)
        # 构造增强响应
        currentTime = SYSTIME
        currentTimeBytes = currentTime.toString(0, 3)
        enhancedResponse = INPUT + currentMsgId + currentTimeBytes
        
        # 发送给云端
        IF (cloudConnected == 1)
            OUTPUT = "[RESPONSE] Enhanced response sent: " + enhancedResponse.prtString()
        END
        
        # 重置状态
        processingState = 0
        waitingForResponse = 0
        currentMsgId = ""
    ELSE
        OUTPUT = "[INFO] Unexpected UART data forwarded"
        IF (cloudConnected == 1)
            OUTPUT = INPUT
        END
    END
    RETURN(TRUE)
END

# ========== 状态报告 ==========
TIMER StatusReport 5000
    statusMsg = "[STATUS] Queue:" + queueSize.prtString() + 
               " State:" + processingState.prtString() + 
               " Waiting:" + waitingForResponse.prtString() + 
               " Connected:" + cloudConnected.prtString() + 
               " Counter:" + testCounter.prtString()
    OUTPUT = statusMsg
END
