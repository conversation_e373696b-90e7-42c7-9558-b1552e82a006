# 同步消息处理测试脚本
# 简化版本用于HisUI工具测试

# ========== 配置参数 ==========
QUEUE_MAX_SIZE = 3      # 测试用小队列
MSG_TIMEOUT = 3000      # 3秒超时
DEBUG_MODE = 1          # 开启调试

# ========== 全局变量 ==========
queueSize = 0
queueHead = 0
queueTail = 0
processingState = 0
currentMsgId = ""
waitingForResponse = 0
lastSendTime = 0
cloudConnected = 0

# 简化队列存储
queue0 = ""
queue1 = ""
queue2 = ""

# 测试计数器
testMsgCount = 0
testRespCount = 0

# ========== 队列操作函数 ==========
FUNCTION enqueue(message)
    result = 0
    IF (queueSize < QUEUE_MAX_SIZE)
        IF (queueTail == 0)
            queue0 = message
        END
        IF (queueTail == 1)
            queue1 = message
        END
        IF (queueTail == 2)
            queue2 = message
        END
        
        queueTail = queueTail + 1
        IF (queueTail >= QUEUE_MAX_SIZE)
            queueTail = 0
        END
        queueSize = queueSize + 1
        result = 1
    END
    RETURN(result)
END

FUNCTION dequeue()
    message = ""
    IF (queueSize > 0)
        IF (queueHead == 0)
            message = queue0
            queue0 = ""
        END
        IF (queueHead == 1)
            message = queue1
            queue1 = ""
        END
        IF (queueHead == 2)
            message = queue2
            queue2 = ""
        END
        
        queueHead = queueHead + 1
        IF (queueHead >= QUEUE_MAX_SIZE)
            queueHead = 0
        END
        queueSize = queueSize - 1
    END
    RETURN(message)
END

FUNCTION extractCmd(fullMessage)
    msgLen = fullMessage.length()
    IF (msgLen > 8)
        originalCmd = fullMessage.subString(0, msgLen - 8)
        currentMsgId = fullMessage.subString(msgLen - 8, msgLen - 4)
        RETURN(originalCmd)
    END
    RETURN("")
END

FUNCTION testLog(message)
    logMsg = "[TEST] " + message
    OUTPUT = logMsg
    RETURN(logMsg)
END

# ========== 连接管理 ==========
CONN SOCK netp
    cloudConnected = 1
    testLog("Cloud connected")
END

DISCONN SOCK netp
    cloudConnected = 0
    processingState = 0
    waitingForResponse = 0
    testLog("Cloud disconnected")
END

# ========== 消息接收处理 ==========
RECV SOCK netp
    IF (cloudConnected == 1)
        testMsgCount = testMsgCount + 1
        enqueueResult = enqueue(INPUT)
        IF (enqueueResult == 1)
            msg = "MSG#" + testMsgCount.prtString() + " queued, size=" + queueSize.prtString()
            testLog(msg)
        ELSE
            msg = "MSG#" + testMsgCount.prtString() + " DROPPED - queue full"
            testLog(msg)
        END
    END
    RETURN(FALSE)
END

# ========== 设备响应处理 ==========
RECV UART uart0
    IF (waitingForResponse == 1)
        testRespCount = testRespCount + 1
        currentTime = SYSTIME
        currentTimeBytes = currentTime.toString(0, 3)
        
        # 构造响应消息
        responseWithId = INPUT + currentMsgId + currentTimeBytes
        
        IF (cloudConnected == 1)
            OUTPUT = responseWithId
            msg = "RESP#" + testRespCount.prtString() + " sent with ID+timestamp"
            testLog(msg)
        END
        
        processingState = 0
        waitingForResponse = 0
        currentMsgId = ""
    ELSE
        msg = "Unexpected UART data: forwarded"
        testLog(msg)
        IF (cloudConnected == 1)
            OUTPUT = INPUT
        END
    END
    RETURN(TRUE)
END

# ========== 主处理循环 ==========
TIMER MessageProcessor 1000
    currentTime = SYSTIME
    
    # 超时检查
    IF (waitingForResponse == 1)
        IF ((currentTime - lastSendTime) > MSG_TIMEOUT)
            testLog("TIMEOUT - resetting state")
            processingState = 0
            waitingForResponse = 0
            currentMsgId = ""
        END
    END
    
    # 处理队列
    IF (processingState == 0)
        IF (queueSize > 0)
            fullMessage = dequeue()
            IF (fullMessage != "")
                originalCmd = extractCmd(fullMessage)
                IF (originalCmd != "")
                    OUTPUT = originalCmd
                    processingState = 1
                    waitingForResponse = 1
                    lastSendTime = currentTime
                    testLog("CMD sent to device, waiting response")
                ELSE
                    testLog("Invalid message format")
                END
            END
        END
    END
END

# ========== 状态报告 ==========
TIMER TestReport 5000
    statusMsg = "=== STATUS === Queue:" + queueSize.prtString() + 
               " State:" + processingState.prtString() + 
               " Connected:" + cloudConnected.prtString() + 
               " Msgs:" + testMsgCount.prtString() + 
               " Resps:" + testRespCount.prtString()
    testLog(statusMsg)
END
