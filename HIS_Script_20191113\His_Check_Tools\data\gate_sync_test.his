# 道闸同步消息处理完整测试脚本
# 用于HisUI工具测试验证

# ========== 配置参数 ==========
QUEUE_MAX_SIZE = 5
MSG_TIMEOUT = 3000
DEBUG_MODE = 1

# ========== 全局变量 ==========
queueSize = 0
queueHead = 0
queueTail = 0
processingState = 0
currentMsgId = ""
waitingForResponse = 0
lastSendTime = 0
cloudConnected = 0

# 队列存储
queue0 = ""
queue1 = ""
queue2 = ""
queue3 = ""
queue4 = ""

# 统计信息
msgReceived = 0
msgProcessed = 0
respSent = 0
timeouts = 0

# 道闸命令定义
gateOpenCmd = [0x9a, 0x01, 0x66, 0x01, 0x01, 0x00, 0x3b, 0x9f]
gateCloseCmd = [0x9a, 0x01, 0x67, 0x01, 0x01, 0x00, 0xb4, 0x9f]
gateStatusCmd = [0x9a, 0x01, 0x78, 0x01, 0x00, 0x00, 0xd2, 0x9f]

# 道闸响应定义
gateOpenResp = [0x9a, 0x01, 0x66, 0x00, 0x01, 0x02, 0x2c, 0x9f]
gateCloseResp = [0x9a, 0x01, 0x67, 0x00, 0x01, 0x02, 0xa3, 0x9f]
gateStatusResp = [0x9a, 0x01, 0x78, 0x00, 0x00, 0x20, 0x01, 0x00, 0x00, 0xba, 0x9f]

# ========== 队列操作函数 ==========
FUNCTION enqueue(message)
    result = 0
    IF (queueSize < QUEUE_MAX_SIZE)
        IF (queueTail == 0)
            queue0 = message
        END
        IF (queueTail == 1)
            queue1 = message
        END
        IF (queueTail == 2)
            queue2 = message
        END
        IF (queueTail == 3)
            queue3 = message
        END
        IF (queueTail == 4)
            queue4 = message
        END
        
        queueTail = queueTail + 1
        IF (queueTail >= QUEUE_MAX_SIZE)
            queueTail = 0
        END
        queueSize = queueSize + 1
        result = 1
    END
    RETURN(result)
END

FUNCTION dequeue()
    message = ""
    IF (queueSize > 0)
        IF (queueHead == 0)
            message = queue0
            queue0 = ""
        END
        IF (queueHead == 1)
            message = queue1
            queue1 = ""
        END
        IF (queueHead == 2)
            message = queue2
            queue2 = ""
        END
        IF (queueHead == 3)
            message = queue3
            queue3 = ""
        END
        IF (queueHead == 4)
            message = queue4
            queue4 = ""
        END
        
        queueHead = queueHead + 1
        IF (queueHead >= QUEUE_MAX_SIZE)
            queueHead = 0
        END
        queueSize = queueSize - 1
    END
    RETURN(message)
END

FUNCTION extractMessageParts(fullMessage)
    msgLen = fullMessage.length()
    IF (msgLen > 8)
        originalCmd = fullMessage.subString(0, msgLen - 8)
        currentMsgId = fullMessage.subString(msgLen - 8, msgLen - 4)
        RETURN(originalCmd)
    END
    RETURN("")
END

FUNCTION debugLog(message)
    IF (DEBUG_MODE == 1)
        OUTPUT = "[DEBUG] " + message
    END
END

FUNCTION getCommandName(cmdBytes)
    cmdName = "UNKNOWN"
    IF (cmdBytes == gateOpenCmd)
        cmdName = "OPEN"
    END
    IF (cmdBytes == gateCloseCmd)
        cmdName = "CLOSE"
    END
    IF (cmdBytes == gateStatusCmd)
        cmdName = "STATUS"
    END
    RETURN(cmdName)
END

FUNCTION simulateDeviceResponse(cmdBytes)
    response = ""
    IF (cmdBytes == gateOpenCmd)
        response = gateOpenResp
    END
    IF (cmdBytes == gateCloseCmd)
        response = gateCloseResp
    END
    IF (cmdBytes == gateStatusCmd)
        response = gateStatusResp
    END
    RETURN(response)
END

# ========== 连接管理 ==========
CONN SOCK netp
    cloudConnected = 1
    debugLog("Cloud connected - System ready")
END

DISCONN SOCK netp
    cloudConnected = 0
    processingState = 0
    waitingForResponse = 0
    debugLog("Cloud disconnected - System reset")
END

# ========== 云端消息接收 ==========
RECV SOCK netp
    IF (cloudConnected == 1)
        msgReceived = msgReceived + 1
        enqueueResult = enqueue(INPUT)
        IF (enqueueResult == 1)
            debugLog("Message #" + msgReceived.prtString() + " enqueued (Queue: " + queueSize.prtString() + ")")
        ELSE
            debugLog("Message #" + msgReceived.prtString() + " DROPPED - Queue full!")
        END
    END
    RETURN(FALSE)
END

# ========== 设备响应处理 ==========
RECV UART uart0
    IF (waitingForResponse == 1)
        respSent = respSent + 1
        currentTime = SYSTIME
        currentTimeBytes = currentTime.toString(0, 3)
        
        # 构造增强响应
        enhancedResponse = INPUT + currentMsgId + currentTimeBytes
        
        IF (cloudConnected == 1)
            OUTPUT = enhancedResponse
            debugLog("Response #" + respSent.prtString() + " sent with ID and timestamp")
        END
        
        # 重置状态
        processingState = 0
        waitingForResponse = 0
        currentMsgId = ""
        
    ELSE
        debugLog("Unexpected UART data - forwarding as-is")
        IF (cloudConnected == 1)
            OUTPUT = INPUT
        END
    END
    RETURN(TRUE)
END

# ========== 主处理循环 ==========
TIMER MessageProcessor 500
    currentTime = SYSTIME
    
    # 检查超时
    IF (waitingForResponse == 1)
        IF ((currentTime - lastSendTime) > MSG_TIMEOUT)
            timeouts = timeouts + 1
            debugLog("TIMEOUT #" + timeouts.prtString() + " - Resetting state")
            processingState = 0
            waitingForResponse = 0
            currentMsgId = ""
        END
    END
    
    # 处理队列中的消息
    IF (processingState == 0)
        IF (queueSize > 0)
            fullMessage = dequeue()
            IF (fullMessage != "")
                originalCmd = extractMessageParts(fullMessage)
                IF (originalCmd != "")
                    msgProcessed = msgProcessed + 1
                    cmdName = getCommandName(originalCmd)
                    
                    # 发送给设备
                    OUTPUT = originalCmd
                    processingState = 1
                    waitingForResponse = 1
                    lastSendTime = currentTime
                    
                    debugLog("Processing #" + msgProcessed.prtString() + " - " + cmdName + " command sent")
                    
                    # 模拟设备响应（测试用）
                    deviceResp = simulateDeviceResponse(originalCmd)
                    IF (deviceResp != "")
                        # 模拟延迟后的设备响应
                        # 在实际环境中，这个响应会来自UART
                        debugLog("Simulated device response for " + cmdName)
                    END
                ELSE
                    debugLog("Invalid message format - skipping")
                END
            END
        END
    END
END

# ========== 状态监控 ==========
TIMER StatusReport 3000
    statusMsg = "=== SYSTEM STATUS ===" + 
               " Queue:" + queueSize.prtString() + "/" + QUEUE_MAX_SIZE.prtString() +
               " State:" + processingState.prtString() +
               " Connected:" + cloudConnected.prtString() +
               " Received:" + msgReceived.prtString() +
               " Processed:" + msgProcessed.prtString() +
               " Responses:" + respSent.prtString() +
               " Timeouts:" + timeouts.prtString()
    debugLog(statusMsg)
END

# ========== 测试数据生成器 ==========
TIMER TestDataGenerator 10000
    IF (cloudConnected == 1)
        # 生成测试消息
        testId = [0x12, 0x34, 0x56, 0x78]
        testTimestamp = SYSTIME.toString(0, 3)
        
        # 创建开闸命令消息
        testMessage = gateOpenCmd + testId + testTimestamp
        
        # 添加到队列
        enqueueResult = enqueue(testMessage)
        IF (enqueueResult == 1)
            debugLog("Auto-generated test message added to queue")
        ELSE
            debugLog("Failed to add auto-generated message - queue full")
        END
    END
END
